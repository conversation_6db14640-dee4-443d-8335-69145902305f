# Repository Guidelines

## Project Structure & Module Organization
- `tongdao-ccsp-all/`: Maven multi‑module Java backend (e.g., beans, common, console, management, sdk). Code follows the standard Maven layout `src/main` and `src/test`.
- `tongdao-ccsp-gmmid-management-web/`: Vue 2 + Element‑UI frontend (`src/`, `tests/`, `public/`).
- `scripts/`: Docker Swarm deployment/ops scripts (see `scripts/README.md`).
- `docs/`: Deployment and operational notes (Traefik, container management).
- `config/`: Config templates (e.g., Traefik). Root `.env.example` shows common environment variables.

## Build, Test, and Development Commands
- Backend (all modules): `mvn -f tongdao-ccsp-all/pom.xml clean package -DskipTests`
- Backend (tests): `mvn -f tongdao-ccsp-all/pom.xml test`
- Backend (run module): `mvn -f tongdao-ccsp-all/tongdao-ccsp-gmmid-management/pom.xml spring-boot:run`
- Frontend (install): `cd tongdao-ccsp-gmmid-management-web && npm install`
- Frontend (dev): `npm run dev`  • (build): `npm run build:prod`  • (lint): `npm run lint`
- Frontend (tests): `npm run test:unit`

## Coding Style & Naming Conventions
- Java: 4‑space indent, classes `PascalCase`, methods/fields `camelCase`, packages lowercase. Keep module boundaries clear (only expose APIs from `*-common`/`*-sdk` as needed).
- Vue/JS: ESLint is enforced (`.eslintrc.js`): 2‑space indent, single quotes, no semicolons, Vue component name property `PascalCase`. Prefer file names `kebab-case.vue`.
- Config: Do not hardcode secrets; read from env or Docker secrets/configs.

## 开发流程
1. **先计划**：始终从讨论方法开始
2. **确定决策**：列出所有需要做出的实施选择
3. **协商方案**：当存在多种方法时，提出权衡利弊
4. **确认一致**：确保在编写代码之前就方法达成一致
5. **然后实施**：仅在我们就计划达成一致后才编写代码


## 核心行为
- 实事求是，对必要信息不做假设，积极地询问、获取
- 当发现问题时，提供建设性反馈
- 对错误逻辑或做法提出异议

## 规划时
- 紧密贴合需求，仅做最小实现，避免过度设计
- 指出潜在的边界情况及其处理方式
- 质疑不合理的设计决策
- 分享最佳实践意见，但要明确哪些是主观观点，哪些是客观事实

## 实施时
- 严格遵循既定计划
- 如果发现意料之外的问题，立即暂停并发起讨论
- 如在实现过程中发现问题，在代码中以行内注释方式指出

## 技术讨论指南
- 对于常见的编程概念，无需过度解释
- 指出潜在的错误、性能问题，或可维护性问题

## 解决问题的思路
- 当你难以通过阅读代码定位问题的时候，记得还有我可以协助你！比如可以在关键点添加打印日志并要求我测试后反馈给你，或者要求你能想到的其他帮助



Always respond in Chinese-simplified
